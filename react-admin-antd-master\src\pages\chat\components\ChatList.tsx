import React, { useState } from "react";
import {
  Button,
  Input,
  Space,
  Typography,
  message,
  Empty,
  Avatar,
  Badge,
} from "antd";
import {
  PlusOutlined,
  MessageOutlined,
  MenuFoldOutlined,
  CaretRightOutlined,
  RobotOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { useNavigate } from "react-router-dom";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { ChatSession } from "../types";
import { getAgentById } from "../data/agents";
import styles from "./ChatList.module.scss";

const { Text } = Typography;
const { Search } = Input;

interface ChatListProps {
  onSelectSession?: (sessionIndex: number) => void;
  style?: React.CSSProperties;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const ChatList: React.FC<ChatListProps> = observer(
  ({ onSelectSession, style, collapsed = false, onToggleCollapse }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();
    const navigate = useNavigate();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [searchExpanded, setSearchExpanded] = useState(false);
    const [editingSessionId, setEditingSessionId] = useState<string | null>(
      null
    );
    const [editingTitle, setEditingTitle] = useState("");
    const [collapsedAgents, setCollapsedAgents] = useState<string[]>([]);

    // 获取智能体会话分组
    const getAgentSessionsGrouped = () => {
      const sessionsByAgent = chatStore.getSessionsByAgent();
      const result: Array<{
        agentId: string;
        agent: any;
        sessions: ChatSession[];
      }> = [];

      Object.entries(sessionsByAgent).forEach(([agentId, sessions]) => {
        const agent = getAgentById(agentId);
        if (agent) {
          const filteredSessions = sessions
            .filter((session) => session.messages.length > 0)
            .filter(
              (session) =>
                session.topic
                  .toLowerCase()
                  .includes(searchKeyword.toLowerCase()) ||
                session.messages.some((msg) =>
                  typeof msg.content === "string"
                    ? msg.content
                        .toLowerCase()
                        .includes(searchKeyword.toLowerCase())
                    : false
                )
            )
            // 按最新更新时间排序，最新的在最上面
            .sort((a, b) => b.lastUpdate - a.lastUpdate);

          if (filteredSessions.length > 0) {
            result.push({ agentId, agent, sessions: filteredSessions });
          }
        }
      });

      return result;
    };

    const agentSessionsGrouped = getAgentSessionsGrouped();

    // 切换智能体折叠状态
    const toggleAgentCollapse = (agentId: string) => {
      setCollapsedAgents((prev) =>
        prev.includes(agentId)
          ? prev.filter((id) => id !== agentId)
          : [...prev, agentId]
      );
    };

    const handleShowAgentSelector = () => {
      navigate("/chat/agents");
    };

    const handleNewChat = () => {
      try {
        const session = chatStore.newInitialSession();
        navigate(`/chat?sessionId=${session.id}`);
        message.success("已创建新聊天");
      } catch (error) {
        console.error("创建新聊天失败:", error);
        message.error("创建新聊天失败，请重试");
      }
    };

    const handleToggleSearch = () => {
      setSearchExpanded(!searchExpanded);
      if (!searchExpanded) {
        // 展开时清空搜索关键词
        setSearchKeyword("");
      }
    };

    const handleCreateAgentSession = (agentId: string) => {
      try {
        const session = chatStore.newAgentSession(agentId);
        navigate(`/chat?sessionId=${session.id}`);
        message.success("已创建新对话");
      } catch (error) {
        console.error("创建智能体会话失败:", error);
        message.error("创建对话失败，请重试");
      }
    };

    const handleSelectSession = (index: number) => {
      console.log(
        "Clicking session:",
        index,
        "Current:",
        chatStore.currentSessionIndex
      );

      const session = chatStore.sessions[index];
      if (session) {
        // 检查会话是否有正在进行的操作
        if (chatStore.hasPendingOperations(session.id)) {
          message.warning("该会话正在处理中，请稍后再试");
          return;
        }

        // 使用安全的会话选择方法
        const success = chatStore.selectSession(index);
        if (success) {
          onSelectSession?.(index);
          // 导航到带有sessionId的聊天路由
          navigate(`/chat?sessionId=${session.id}`);
        } else {
          message.warning("无法切换到该会话，请稍后再试");
          return;
        }
      }

      // 通知父组件会话已切换
      if (onSelectSession) {
        onSelectSession(index);
      }
    };

    const handleSaveTitle = () => {
      if (editingSessionId && editingTitle.trim()) {
        const sessionIndex = chatStore.sessions.findIndex(
          (s) => s.id === editingSessionId
        );
        if (sessionIndex >= 0) {
          chatStore.updateMessage(sessionIndex, -1, () => {
            chatStore.sessions[sessionIndex].topic = editingTitle.trim();
          });
          message.success("标题已更新");
        }
      }
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const handleCancelEdit = () => {
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const getLastMessage = (session: ChatSession) => {
      if (session.messages.length === 0) return "暂无消息";

      const lastMessage = session.messages[session.messages.length - 1];
      const content =
        typeof lastMessage.content === "string"
          ? lastMessage.content
          : lastMessage.content
              .filter((c) => c.type === "text")
              .map((c) => c.text)
              .join("");

      // 清理markdown格式和多余空白
      const cleanContent = content
        .replace(/[#*`_~\[\]()]/g, "") // 移除markdown符号
        .replace(/\n+/g, " ") // 换行替换为空格
        .replace(/\s+/g, " ") // 多个空格合并为一个
        .trim();

      return (
        cleanContent.slice(0, 60) + (cleanContent.length > 60 ? "..." : "")
      );
    };

    // 格式化时间显示
    const formatTime = (timestamp: number) => {
      const now = Date.now();
      const diff = now - timestamp;
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (minutes < 1) return "刚刚";
      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      if (days < 7) return `${days}天前`;

      return new Date(timestamp).toLocaleDateString();
    };

    return (
      <div className={styles.chatListContainer} style={style}>
        {/* 头部操作区 */}
        <div className={styles.header}>
          <div className={styles.headerTop}>
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={onToggleCollapse}
              className={styles.collapseButton}
              style={{
                transform: collapsed ? "rotate(180deg)" : "rotate(0deg)",
                transition: "transform 0.2s",
              }}
            />

            <div className={styles.searchContainer}>
              {searchExpanded ? (
                <Search
                  placeholder="搜索对话..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onBlur={() => {
                    if (!searchKeyword) {
                      setSearchExpanded(false);
                    }
                  }}
                  allowClear
                  autoFocus
                  size="small"
                />
              ) : (
                <Button
                  type="text"
                  icon={<SearchOutlined />}
                  onClick={handleToggleSearch}
                  className={styles.searchButton}
                />
              )}
            </div>
          </div>

          <div className={styles.buttonGroup}>
            <Button
              type="default"
              icon={<PlusOutlined />}
              onClick={handleNewChat}
              block
              className={styles.newChatButton}
            >
              新建聊天
            </Button>

            <Button
              type="default"
              icon={<RobotOutlined />}
              onClick={handleShowAgentSelector}
              block
              className={styles.newChatButton}
            >
              选择智能体
            </Button>
          </div>
        </div>

        {/* 聊天列表 */}
        <div className={styles.chatListContent}>
          {agentSessionsGrouped.length === 0 ? (
            <Empty description="暂无聊天记录" className={styles.emptyList} />
          ) : (
            <div className={styles.sessionGroups}>
              {/* 智能体会话分组 */}
              {agentSessionsGrouped.map(({ agentId, agent, sessions }) => {
                const isCollapsed = collapsedAgents.includes(agentId);

                return (
                  <div key={agentId} className={styles.agentGroup}>
                    <div className={styles.agentHeader}>
                      <div
                        className={styles.agentInfo}
                        onClick={() => toggleAgentCollapse(agentId)}
                      >
                        <Avatar
                          src={agent.avatar}
                          size={24}
                          style={{ backgroundColor: "#1890ff" }}
                        >
                          {agent.name.charAt(0)}
                        </Avatar>
                        <Text strong className={styles.agentName}>
                          {agent.name}
                        </Text>
                        <Badge
                          count={sessions.length}
                          size="small"
                          style={{ backgroundColor: "#52c41a" }}
                        />
                        <CaretRightOutlined
                          className={`${styles.collapseIcon} ${
                            isCollapsed ? styles.collapsed : styles.expanded
                          }`}
                        />
                      </div>

                      <Button
                        type="text"
                        size="small"
                        icon={<PlusOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateAgentSession(agentId);
                        }}
                        className={styles.addSessionButton}
                        title={`创建${agent.name}对话`}
                      />
                    </div>

                    {!isCollapsed && (
                      <div className={styles.agentSessions}>
                        {sessions.map((session) => {
                          const actualIndex = chatStore.sessions.findIndex(
                            (s) => s.id === session.id
                          );
                          const isActive =
                            actualIndex === chatStore.currentSessionIndex;
                          const isEditing = editingSessionId === session.id;

                          return (
                            <div
                              key={`${session.id}-${actualIndex}-${isActive}`}
                              className={`${styles.sessionItem} ${
                                isActive ? styles.active : styles.inactive
                              }`}
                              onClick={() => {
                                if (!isEditing) {
                                  handleSelectSession(actualIndex);
                                }
                              }}
                            >
                              <div className={styles.sessionContent}>
                                <MessageOutlined
                                  className={styles.messageIcon}
                                />
                                <div className={styles.sessionInfo}>
                                  {isEditing ? (
                                    <Input
                                      value={editingTitle}
                                      onChange={(e) =>
                                        setEditingTitle(e.target.value)
                                      }
                                      onPressEnter={handleSaveTitle}
                                      onBlur={handleSaveTitle}
                                      onKeyDown={(e) => {
                                        if (e.key === "Escape") {
                                          handleCancelEdit();
                                        }
                                      }}
                                      size="small"
                                      autoFocus
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  ) : (
                                    <Text
                                      strong={isActive}
                                      className={`${styles.sessionTitle} ${
                                        isActive
                                          ? styles.active
                                          : styles.inactive
                                      }`}
                                      style={{
                                        fontSize: configStore.fontSize * 1,
                                      }}
                                    >
                                      {session.topic}
                                    </Text>
                                  )}
                                  <div
                                    style={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                      alignItems: "flex-start",
                                      gap: "8px",
                                    }}
                                  >
                                    <Text
                                      type="secondary"
                                      style={{
                                        fontSize: configStore.fontSize * 0.8,
                                        flex: 1,
                                        lineHeight: "1.4",
                                      }}
                                      ellipsis
                                    >
                                      {getLastMessage(session)}
                                    </Text>
                                    <Text
                                      type="secondary"
                                      style={{
                                        fontSize: configStore.fontSize * 0.7,
                                        whiteSpace: "nowrap",
                                        opacity: 0.7,
                                      }}
                                    >
                                      {formatTime(session.lastUpdate)}
                                    </Text>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  }
);
