if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()}).then(()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(i,t)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let o={};const r=e=>n(e,c),f={module:{uri:c},exports:o,require:r};s[c]=Promise.all(i.map(e=>f[e]||r(e))).then(e=>(t(...e),o))}}define(["./workbox-be2b045e"],function(e){"use strict";self.skipWaiting(),e.clients<PERSON>laim(),e.precacheAndRoute([{url:"fonts/demo_index.html",revision:"8f92860ac0d3cfe2b5389184a3519ad3"},{url:"fonts/demo.css",revision:"31103ad158e19b978f7e730ff5ac959b"},{url:"fonts/iconfont.css",revision:"c8201b9ffb8ab098a936d3453154d12b"},{url:"fonts/iconfont.js",revision:"b1dcf3f5ecda1f6ece07cba5e886bccc"},{url:"manifest.webmanifest",revision:"30745bb998e34cec18350a1e93fa53e0"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/api\./i,new e.NetworkFirst({cacheName:"api-cache",networkTimeoutSeconds:10,plugins:[new e.CacheableResponsePlugin({statuses:[0,200]})]}),"GET"),e.registerRoute(/\.(js|css|png|jpg|jpeg|svg|gif)$/,new e.StaleWhileRevalidate({cacheName:"assets-cache",plugins:[]}),"GET")});
//# sourceMappingURL=sw.js.map
