/* ChatList 组件样式 */

.chatListContainer {
  height: 100%;
  background: #f7f7fc;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .headerTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .collapseButton {
      padding: 4px;
      border-radius: 4px;

      &:hover {
        background: #f0f0f0;
      }
    }

    .searchContainer {
      flex: 1;
      display: flex;
      justify-content: flex-end;

      .searchButton {
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #f0f0f0;
        }
      }
    }
  }

  .buttonGroup {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.newChatButton {
  background-color: #ffffff;

  color: #4318ff;
  &:hover {
    background-color: #eeeeee !important;
    border-color: #f8f8f8 !important;
    color: #4318ff !important;
  }
}

.divider {
  margin: 12px 0;
}

.navigationButtons {
  width: 100%;
}

.navigationButton {
  text-align: left;
  justify-content: flex-start;

  &.active {
    background-color: #7b4ffe;
    border-color: #7b4ffe;
    color: #fff;
  }

  &.inactive {
    background-color: transparent;
    border-color: #d9d9d9;
    color: #666;
  }
}

.searchInput {
  // 使用默认样式
}

.chatListContent {
  flex: 1;
  overflow: auto;
  padding: 16px 16px 20px 16px; /* 增加底部 padding 确保最后一项边框完全显示 */

  .emptyList {
    margin-top: 60px;
  }

  .sessionGroups {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .agentGroup {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .agentHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;

      &:hover {
        background: #f0f0f0;
      }

      .agentInfo {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        flex: 1;

        .agentName {
          font-size: 14px;
          color: #333;
        }

        .collapseIcon {
          font-size: 12px;
          color: #666;
          transition: transform 0.2s;
          margin-left: auto;

          &.expanded {
            transform: rotate(90deg);
          }

          &.collapsed {
            transform: rotate(0deg);
          }
        }
      }

      .addSessionButton {
        padding: 4px 8px;
        border-radius: 4px;
        color: #603cff;
        &:hover {
          // background: #f6ffed;
          color: #603cff;
        }
      }
    }

    .agentSessions {
      padding: 8px;
    }
  }

  .normalSessions {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .sectionHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .sessionItem {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;

    &:hover {
      background: #f8f8f8;
    }

    &.active {
      background: #f6f2ff;
      border-radius: 15px;
    }

    .sessionContent {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      position: relative;

      .messageIcon {
        font-size: 16px;
        color: #666;
        margin-top: 2px;
      }

      .sessionInfo {
        flex: 1;
        min-width: 0;

        .sessionTitle {
          display: block;
          margin-bottom: 4px;
          font-size: 14px;
          line-height: 1.4;

          &.active {
            color: #6f3bfe;
            font-weight: 600;
          }

          &.inactive {
            color: #333;
            font-weight: 400;
          }
        }
      }

      .sessionActions {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0;
        transition: opacity 0.2s ease;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        padding: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .moreButton {
          color: #666;
          &:hover {
            color: #333;
            background: rgba(0, 0, 0, 0.04);
          }
        }
      }
    }

    &:hover .sessionActions {
      opacity: 1;
    }
  }
}

.chatListItem {
  padding: 10px 14px;
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease; /* 包含边框和阴影的过渡效果 */
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 2px solid transparent !important; /* 使用 !important 确保自定义边框生效 */
  position: relative;
  width: 100%; /* 确保占满容器宽度 */
  box-sizing: border-box; /* 包含边框在内的盒模型 */

  &.active {
    border-color: #7b4ffe !important; /* 使用 !important 确保激活状态边框生效 */
    background-color: #ffffff;
    box-shadow: 0px 0px 3px 1px rgb(129 129 129 / 50%);
    margin-bottom: 12px; /* 激活状态时增加底部边距，为阴影留出空间 */
  }

  /* 确保最后一项有足够的空间显示边框和阴影 */
  &:last-child {
    margin-bottom: 16px; /* 最后一项增加更多底部边距 */

    &.active {
      margin-bottom: 20px; /* 最后一项激活状态时更多边距 */
    }
  }

  &:hover {
    background-color: #f8f8f8;
  }
  :global .ant-list-item-meta {
    display: flex;
    align-items: center;
  }
}

.messageIcon {
  font-size: 16px;
  // color: #7b4ffe;
}

.sessionTitle {
  &.active {
    // color: #7b4ffe;
    font-weight: 600;
  }

  &.inactive {
    color: #333;
    font-weight: 400;
  }
}

.sessionDescription {
  margin-bottom: 4px;
}

/* .sessionMeta 使用默认样式 */

.moreButton {
  opacity: 0.6;

  &:hover {
    opacity: 1;
  }
}

/* .editInput 使用默认样式，点击事件阻止冒泡在JS中处理 */

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }

  .chatListItem {
    padding: 10px 12px;
  }
}
