import React from "react";
import { Card, Avatar, Typography, Row, Col, message } from "antd";
import { observer } from "mobx-react-lite";
import { useNavigate } from "react-router-dom";
import { getPopularAgents, getAgentById } from "../data/agents";
import { useChatStoreOnly } from "../stores";
import styles from "./PopularAgents.module.scss";

const { Text } = Typography;

interface PopularAgentsProps {
  onAgentSelect?: (agentId: string) => void;
  sessionId?: string; // 当前会话ID，用于更新现有会话
}

export const PopularAgents: React.FC<PopularAgentsProps> = observer(
  ({ onAgentSelect, sessionId }) => {
    const chatStore = useChatStoreOnly();
    const navigate = useNavigate();
    const popularAgents = getPopularAgents(6);

    const handleAgentClick = (agentId: string) => {
      try {
        if (sessionId) {
          // 如果有sessionId，更新现有会话的智能体信息
          const sessionIndex = chatStore.sessions.findIndex(
            (s) => s.id === sessionId
          );
          if (sessionIndex >= 0) {
            const agent = getAgentById(agentId);
            if (agent) {
              chatStore.sessions[sessionIndex].agentId = agentId;
              chatStore.sessions[
                sessionIndex
              ].topic = `与 ${agent.name} 的对话`;
              chatStore.sessions[sessionIndex].memoryPrompt =
                agent.systemPrompt || "";
              chatStore.saveToLocalStorage();

              // 调用回调
              onAgentSelect?.(agentId);

              message.success(
                `已选择智能体：${agent.name}，现在可以开始对话了`
              );
              return;
            }
          }
        }

        // 如果没有sessionId，创建新的智能体会话
        const session = chatStore.newAgentSession(agentId);

        // 导航到聊天界面
        navigate(`/chat?sessionId=${session.id}`);

        // 调用回调
        onAgentSelect?.(agentId);

        message.success(`已选择智能体进行对话`);
      } catch (error) {
        console.error("选择智能体失败:", error);
        message.error("选择智能体失败，请重试");
      }
    };

    return (
      <div className={styles.popularAgents}>
        <div className={styles.header}>
          <Text type="secondary" className={styles.title}>
            选择智能体开始对话
          </Text>
        </div>

        <Row gutter={[12, 12]} className={styles.agentGrid}>
          {popularAgents.map((agent) => (
            <Col key={agent.id} xs={12} sm={8} md={8} lg={8} xl={4}>
              <Card
                hoverable
                className={styles.agentCard}
                onClick={() => handleAgentClick(agent.id)}
                bodyStyle={{ padding: "16px 12px" }}
              >
                <div className={styles.agentContent}>
                  <Avatar
                    src={agent.avatar}
                    size={40}
                    style={{ backgroundColor: "#1890ff" }}
                    className={styles.avatar}
                  >
                    {agent.name.charAt(0)}
                  </Avatar>

                  <div className={styles.agentInfo}>
                    <Text strong className={styles.agentName}>
                      {agent.name}
                    </Text>
                    <Text type="secondary" className={styles.agentCategory}>
                      {agent.category}
                    </Text>
                  </div>

                  <div className={styles.usageCount}>
                    <Text type="secondary" style={{ fontSize: "11px" }}>
                      {agent.usageCount}次使用
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }
);

export default PopularAgents;
