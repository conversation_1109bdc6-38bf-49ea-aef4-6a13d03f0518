/* MessageInput 组件样式 */

.messageInputContainer {
  background-color: #fbfbfb;

  padding: 20px 20px 10px 20px;
  width: 100%;
  &.hasMessages {
    border-top: none;
    margin: 0 auto;
    max-width: 90%;
    // max-width: 600px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  &.noMessages {
    border-top: none;
    margin: 0 auto;
    max-width: 90%;
    // max-width: 600px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.inputWrapper {
  // display: flex;
  align-items: flex-end;
  gap: 12px;
  position: relative;

  .messageInputTools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin: 10px 0;
  }
  .attachmentButton {
    border-radius: 40px;
    &:hover {
      color: #7b4ffe !important;
      border-color: #7b4ffe !important;
    }
  }

  .textareaWrapper {
    flex: 1;
    position: relative;
  }

  .textarea {
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    resize: none;
    box-shadow: none;
    outline: none;
  }

  .sendButton {
    background: linear-gradient(105.46deg, #2c53fa 32.33%, #9a28f6 92.43%);
    border-color: #7b4ffe;
    color: #fff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .emojiPicker {
    position: absolute;
    bottom: 100%;
    right: 0;
    z-index: 1000;
    margin-bottom: 8px;
  }

  .attachedImagesContainer {
    margin-bottom: 12px;
  }

  .imagePreview {
    position: relative;
    display: inline-block;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
  }

  .imagePreviewImg {
    width: 60px;
    height: 60px;
    object-fit: cover;
    display: block;
  }

  .imageRemoveButton {
    position: absolute;
    top: 2px;
    right: 2px;
    min-width: 20px;
    height: 20px;
    padding: 0;
    font-size: 12px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
}
