{"version": 3, "file": "sw.js", "sources": ["../../../AppData/Local/Temp/1c13ec6e3b138a9a927cfba477ce2057/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-routing@7.3.0/node_modules/workbox-routing/registerRoute.mjs';\nimport {CacheableResponsePlugin as workbox_cacheable_response_CacheableResponsePlugin} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-cacheable-response@7.3.0/node_modules/workbox-cacheable-response/CacheableResponsePlugin.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-strategies@7.3.0/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {StaleWhileRevalidate as workbox_strategies_StaleWhileRevalidate} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-strategies@7.3.0/node_modules/workbox-strategies/StaleWhileRevalidate.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-core@7.3.0/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-precaching@7.3.0/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-precaching@7.3.0/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-routing@7.3.0/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'C:/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/workbox-precaching@7.3.0/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"fonts/demo_index.html\",\n    \"revision\": \"8f92860ac0d3cfe2b5389184a3519ad3\"\n  },\n  {\n    \"url\": \"fonts/demo.css\",\n    \"revision\": \"31103ad158e19b978f7e730ff5ac959b\"\n  },\n  {\n    \"url\": \"fonts/iconfont.css\",\n    \"revision\": \"c8201b9ffb8ab098a936d3453154d12b\"\n  },\n  {\n    \"url\": \"fonts/iconfont.js\",\n    \"revision\": \"b1dcf3f5ecda1f6ece07cba5e886bccc\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"30745bb998e34cec18350a1e93fa53e0\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/api\\./i, new workbox_strategies_NetworkFirst({ \"cacheName\":\"api-cache\",\"networkTimeoutSeconds\":10, plugins: [new workbox_cacheable_response_CacheableResponsePlugin({ statuses: [ 0, 200 ] })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(js|css|png|jpg|jpeg|svg|gif)$/, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"assets-cache\", plugins: [] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_NetworkFirst", "cacheName", "networkTimeoutSeconds", "plugins", "workbox_cacheable_response_CacheableResponsePlugin", "statuses", "workbox_strategies_StaleWhileRevalidate"], "mappings": "inBA2BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,wBACPC,SAAY,oCAEd,CACED,IAAO,iBACPC,SAAY,oCAEd,CACED,IAAO,qBACPC,SAAY,oCAEd,CACED,IAAO,oBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,gBAG7GC,EAAAA,cAA8B,oBAAqB,IAAIC,eAAgC,CAAEC,UAAY,YAAYC,sBAAwB,GAAIC,QAAS,CAAC,IAAIC,0BAAmD,CAAEC,SAAU,CAAE,EAAG,UAAc,OAC7ON,EAAAA,cAA8B,mCAAoC,IAAIO,uBAAwC,CAAEL,UAAY,eAAgBE,QAAS,KAAO"}